import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Chip,
  Button,
  Breadcrumbs,
  Link,
  Divider
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import axios from '../../utils/axios';
import Iconify from '../../components/Iconify';

// Import chart components
import IncomeOverviewChart from '../../components/admin/income/IncomeOverviewChart';
import IncomeGrowthChart from '../../components/admin/income/IncomeGrowthChart';
import IncomeTrendChart from '../../components/admin/income/IncomeTrendChart';
import IncomeFilters from '../../components/admin/income/IncomeFilters';
import IncomeExportButton from '../../components/admin/income/IncomeExportButton';
import IncomeMetricsCards from '../../components/admin/income/IncomeMetricsCards';
import DailyIncomeList from '../../components/admin/income/DailyIncomeList';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`income-tabpanel-${index}`}
      aria-labelledby={`income-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function IncomeDashboard() {
  const theme = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State management
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Data state
  const [incomeStats, setIncomeStats] = useState(null);
  const [incomeByPeriod, setIncomeByPeriod] = useState([]);
  const [incomeGrowth, setIncomeGrowth] = useState(null);
  
  // Filter state - simplified to just period selection
  const [filters, setFilters] = useState({
    period: 'month' // day, month, year
  });

  // Load income statistics
  const loadIncomeStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        period: filters.period
      };

      const [statsRes, periodRes, growthRes] = await Promise.all([
        axios.get('/api/admin/income/statistics', { params }),
        axios.get('/api/admin/income/by-period', { params }),
        axios.get('/api/admin/income/growth', { params })
      ]);

      if (statsRes.data.success) {
        setIncomeStats(statsRes.data.data);
      }

      if (periodRes.data.success) {
        setIncomeByPeriod(periodRes.data.data);
      }

      if (growthRes.data.success) {
        setIncomeGrowth(growthRes.data.data);
      }

    } catch (err) {
      console.error('Error loading income data:', err);
      setError(err.response?.data?.message || 'Failed to load income data');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Load data on component mount and filter changes
  useEffect(() => {
    loadIncomeStats();
  }, [loadIncomeStats]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  // Handle refresh
  const handleRefresh = () => {
    loadIncomeStats();
  };

  if (loading && !incomeStats) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }



  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Breadcrumb Navigation */}
        <Box sx={{ mb: 2 }}>
          <Breadcrumbs aria-label="breadcrumb">
            <Link
              component={RouterLink}
              to="/"
              color="inherit"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <Iconify icon="eva:home-fill" sx={{ mr: 0.5 }} width={20} height={20} />
              {t('nav.home', 'Home')}
            </Link>
            <Link
              component={RouterLink}
              to="/admin"
              color="inherit"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <Iconify icon="mdi:shield-account" sx={{ mr: 0.5 }} width={20} height={20} />
              {t('nav.admin', 'Admin')}
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
              <Iconify icon="mdi:cash-multiple" sx={{ mr: 0.5 }} width={20} height={20} />
              {t('income.title', 'Income Monitoring')}
            </Typography>
          </Breadcrumbs>
        </Box>



        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Filters */}
        <IncomeFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onRefresh={handleRefresh}
          loading={loading}
        />

        {/* Key Metrics Cards */}
        {incomeStats && (
          <IncomeMetricsCards
            data={incomeStats}
            growth={incomeGrowth}
            loading={loading}
          />
        )}

        {/* Simplified Dashboard Layout */}
        <Paper sx={{ width: '100%', mt: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label={t('income.tabs.overview', 'Overview')} />
            <Tab label={t('income.tabs.trends', 'Trends')} />
            <Tab label={t('income.tabs.growth', 'Growth Analysis')} />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <IncomeOverviewChart data={incomeStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <IncomeGrowthChart data={incomeGrowth} period={filters.period} />
              </Grid>
              <Grid item xs={12}>
                <IncomeTrendChart
                  data={incomeByPeriod}
                  period={filters.period}
                />
              </Grid>

              {/* Show detailed transaction list for daily view */}
              {filters.period === 'day' && (
                <Grid item xs={12}>
                  <DailyIncomeList
                    period={filters.period}
                    onRefresh={handleRefresh}
                  />
                </Grid>
              )}
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <IncomeTrendChart
                  data={incomeByPeriod}
                  period={filters.period}
                  detailed={true}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <IncomeGrowthChart data={incomeGrowth} period={filters.period} />
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>

        {/* Export Button */}
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <IncomeExportButton filters={filters} />
        </Box>
      </Box>
    </Container>
  );
}
