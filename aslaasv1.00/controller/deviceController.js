const Device = require("../models/device");
const Driver = require("../models/driver");

const {
  sendMqtt,
  checkMqttClient,
  getMqttClients,
} = require("../utils/channel");
const DeviceMessageLogModel = require("../models/deviceMessageLog");
const SimcardSmsLogModel = require("../models/simSmsLog");
const CarGpsModel = require("../models/carGps");
const ADMIN_PHONE_NUMBER = process.env.ADMIN_PHONE_NUMBER;
const User = require("../models/user");
const LogModel = require("../models/log");
const logger = require("../utils/logger");



const {
  unsubscribe,
  subscribe,
  publish,
  subscribeCar2,
  unsubscribeCar2,
} = require("../utils/mqtt");
const Order = require("../models/order");
const { getBankList } = require("../utils/QPayment");
const DriverProfile = require("../models/driverProfile");


const ObjectId = require("mongoose").Types.ObjectId;
// const statisticsMqttPublisher = require("../utils/statisticsMqtt"); // Disabled - using direct MQTT capture instead


const numeral = require("numeral");

let cmds = [];
cmds[":turnon"] = "as";
cmds[":turnoff"] = "untar";
cmds[":lock"] = "lock";
cmds[":unlock"] = "unlock";
cmds[":check"] = "check"; //this command is retrieve from data from gps
cmds[":on1"] = "on1";
cmds[":on2"] = "on2";
cmds[":off1"] = "off1";
cmds[":off2"] = "off2";
cmds[":temp"] = "temp";


const driverConfirm = async (req, res) => {
  try {
    const { phoneNumber, drivername, address } = req.body;
    let driverProfile = await DriverProfile.findOne({ phoneNumber });
    if (driverProfile == null) {
      driverProfile = new DriverProfile({
        phoneNumber,
        drivername,
        address,
      });
      await driverProfile.save();
      // const qpay = await getBankList(req, res, req.user.$set_id);

      //  if (response != null) {

      res.json({ success: true, message: "Order sucess" });
    } else {
      if (!DriverProfile.paid) {
        // const qpay = await getBankList(req, res, req.user._id);
        return res.json({
          success: false,
          message: `Already submitted order `,
          driverProfile,
        });
      }
      return res.json({
        success: false,
        message: `Already submitted order and paid, invoice:${driverProfile.realInvoiceId}`,
        driverProfile,
      });
    }
  } catch (err) {
    logger.error("Error in driver profile creation:", err);
    return res.json({ success: false, message: "Fatal Error" });
  }
};

const getConnectedMqttClients = async (req, res) => {
  try {
    const options = {
      auth: {
        username: process.env.MQTT_USER_NAME,
        password: process.env.MQTT_USER_PWD,
      },
    };
    const response = await getMqttClients(options);

    // Add debug info
    logger.debug('MQTT Clients Debug:', {
      totalClients: response.clients ? response.clients.length : 0,
      sampleClients: response.clients ? response.clients.slice(0, 3).map(c => ({
        clientid: c.clientid,
        connected: c.connected,
        username: c.username
      })) : []
    });

    res.json({ ...response });
  } catch (err) {
    logger.error('Error getting MQTT clients:', err);
    res.json({ success: false, err, clients: [] });
  }
};

const getDevices = async (req, res) => {
  const devices = await Device.find({ phoneNumber: req.body.phoneNumber });

  if (devices && devices.length > 0) {
    return res.json({ success: true, devices });
  } else {
    return res.json({ success: false });
  }
};
const listAll = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      userStatusFilter = 'all',
      versionFilter = 'all',
      lastLogFilter = 'all',
      remainingDaysFilter = 'all'
    } = req.body;
    const skip = (page - 1) * limit;

    // Build match conditions for search
    let matchConditions = {};
    if (search) {
      matchConditions = {
        $or: [
          { deviceNumber: { $regex: search, $options: 'i' } },
          { phoneNumber: { $regex: search, $options: 'i' } },
          { uix: { $regex: search, $options: 'i' } }
        ]
      };
    }

    // Build aggregation pipeline
    let pipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: "users",
          localField: "phoneNumber",
          foreignField: "phoneNumber",
          as: "user",
        },
      },
      // Filter out devices without users
      { $match: { "user.0": { $exists: true } } },
      // Add lookup for latest car GPS log
      {
        $lookup: {
          from: "cargps",
          localField: "deviceNumber",
          foreignField: "deviceNumber",
          as: "deviceLogs",
          pipeline: [
            { $sort: { createdAt: -1 } },
            { $limit: 1 }
          ]
        }
      },
      // Add lookup for latest SIM card SMS log
      {
        $lookup: {
          from: "simcardsmslogs",
          localField: "deviceNumber",
          foreignField: "deviceNumber",
          as: "simLogs",
          pipeline: [
            { $sort: { received: -1 } },
            { $limit: 1 }
          ]
        }
      },
      // Add computed fields for last log time and SIM info
      {
        $addFields: {
          lastLogTime: {
            $cond: {
              if: { $gt: [{ $size: "$deviceLogs" }, 0] },
              then: { $arrayElemAt: ["$deviceLogs.createdAt", 0] },
              else: null
            }
          },
          deviceVersion: {
            $cond: {
              if: { $gt: [{ $size: "$deviceLogs" }, 0] },
              then: { $arrayElemAt: ["$deviceLogs.payload", 0] },
              else: null
            }
          },
          simInfo: {
            $cond: {
              if: { $gt: [{ $size: "$simLogs" }, 0] },
              then: {
                balance: { $arrayElemAt: ["$simLogs.balance", 0] },
                expired: { $arrayElemAt: ["$simLogs.expired", 0] },
                content: { $arrayElemAt: ["$simLogs.content", 0] },
                lastUpdate: { $arrayElemAt: ["$simLogs.received", 0] }
              },
              else: null
            }
          },
          // Add computed user status
          userStatus: {
            $let: {
              vars: {
                user: { $arrayElemAt: ["$user", 0] }
              },
              in: {
                $cond: {
                  if: { $eq: ["$$user.phoneNumber", ADMIN_PHONE_NUMBER] },
                  then: "$$user.status",
                  else: {
                    $cond: {
                      if: { $and: ["$$user.expired", { $gt: ["$$user.expired", new Date()] }] },
                      then: {
                        $cond: {
                          if: { $or: [{ $eq: ["$$user.licenseKey", ""] }, { $eq: ["$$user.licenseKey", null] }] },
                          then: "trial",
                          else: "active"
                        }
                      },
                      else: "expired"
                    }
                  }
                }
              }
            }
          },
          // Add remaining days calculation
          remainingDays: {
            $let: {
              vars: {
                user: { $arrayElemAt: ["$user", 0] }
              },
              in: {
                $cond: {
                  if: "$$user.expired",
                  then: {
                    $divide: [
                      { $subtract: ["$$user.expired", new Date()] },
                      86400000 // milliseconds in a day
                    ]
                  },
                  else: null
                }
              }
            }
          }
        }
      }
    ];

    // Apply filters
    let filterConditions = {};

    // User status filter
    if (userStatusFilter !== 'all') {
      if (userStatusFilter === 'active') {
        filterConditions.userStatus = 'active';
      } else if (userStatusFilter === 'expired') {
        filterConditions.userStatus = { $in: ['expired', 'trial'] };
      }
    }

    // Version filter
    if (versionFilter !== 'all') {
      if (versionFilter === 'unknown') {
        filterConditions.deviceVersion = null;
      } else if (versionFilter === 'old') {
        filterConditions.deviceVersion = { $regex: /1\.[23]\./, $options: 'i' };
      } else if (versionFilter === 'current') {
        filterConditions.deviceVersion = { $regex: /1\.4\./, $options: 'i' };
      }
    }

    // Last log filter
    if (lastLogFilter !== 'all') {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      if (lastLogFilter === 'no_log') {
        filterConditions.$or = [
          { lastLogTime: null },
          { lastLogTime: { $lt: oneWeekAgo } }
        ];
      } else if (lastLogFilter === 'old') {
        filterConditions.lastLogTime = { $gte: oneWeekAgo, $lt: oneDayAgo };
      } else if (lastLogFilter === 'recent') {
        filterConditions.lastLogTime = { $gte: oneDayAgo };
      }
    }

    // Remaining days filter
    if (remainingDaysFilter !== 'all') {
      if (remainingDaysFilter === 'expired') {
        filterConditions.remainingDays = { $lt: 0 };
      } else if (remainingDaysFilter === 'expiring_soon') {
        filterConditions.remainingDays = { $gte: 0, $lte: 7 };
      } else if (remainingDaysFilter === 'active') {
        filterConditions.remainingDays = { $gt: 7 };
      }
    }

    // Add filter stage if we have conditions
    if (Object.keys(filterConditions).length > 0) {
      pipeline.push({ $match: filterConditions });
    }

    // Get total count for pagination (run pipeline without skip/limit)
    const countPipeline = [...pipeline, { $count: "total" }];
    const countResult = await Device.aggregate(countPipeline);
    const totalDevices = countResult.length > 0 ? countResult[0].total : 0;

    // Add pagination
    pipeline.push({ $skip: skip });
    pipeline.push({ $limit: parseInt(limit) });

    let list = await Device.aggregate(pipeline);

    // Status calculation is now done in the aggregation pipeline
    // Just ensure the computed status is set on the user object for compatibility
    list.forEach((row) => {
      if (row.user && row.user[0] && row.userStatus) {
        row.user[0].status = row.userStatus;
      }
    });

    return res.json({
      success: true,
      list,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalDevices / limit),
        totalDevices,
        hasNextPage: page * limit < totalDevices,
        hasPrevPage: page > 1
      }
    });
  } catch (err) {
    logger.error("Error getting devices:", err);
    return res.json({ success: false, err });
  }
};
const getAll = async (req, res) => {
  try {
    let list = await Device.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "phoneNumber",
          foreignField: "phoneNumber",
          as: "user",
        },
      },
    ]);
    list = list.filter((d) => d.user.length > 0);
    list.map((row, index) => {
      let user = row.user[0];
      if (user && user.status) {
        let status = user.status;
        if (user.phoneNumber != ADMIN_PHONE_NUMBER) {
          if (user.expired) {
            const offset = new Date(user.expired).getTime() - Date.now();
            if (offset < 0) {
              status = "expired";
            }
            if (user.licenseKey == undefined || user.licenseKey == "") {
              status = "trial";
            }
          } else {
            status = "trial";
          }
        }

        row.user[0].status = status;
      }
    });
    return res.json({ success: true, list });
  } catch (err) {
    logger.error("Error in getAll:", err);
    return res.json({ success: false, err });
  }
};

const editDevice = async (req, res) => {
  const device = await Device.aggregate([
    {
      $match: {
        _id: ObjectId(req.params.id),
      },
    },
    {
      $lookup: {
        from: "users",
        localField: "phoneNumber",
        foreignField: "phoneNumber",
        as: "user",
      },
    },
  ]);
  return res.json({ success: true, device: device[0] });
};

const myDevice = async (req, res) => {
  const device = await Device.find({ phoneNumber: req.user.phoneNumber });
  if (device != null) {
    return res.json({ success: true, device: device[0], devices: device });
  } else {
    return res.json({ success: false });
  }
};

const setByAdmin = async (req, res) => {
  try {
    const { deviceNumber, type, expired, licenseKey } = req.body;
    const device = await Device.findById(req.params.id);
    device.deviceNumber = deviceNumber;
    device.type = type;
    await device.save();
    const user = await User.findOne({ phoneNumber: device.phoneNumber });
    if (user != null) {
      user.expired = expired;
      user.licenseKey = licenseKey;
      await user.save();
      return res.json({ success: true });
    }
    return res.json({ success: true, message: "Can not update user license" });
  } catch (err) {
    logger.error("Error editing device:", err);
    return res.json({ success: false });
  }
};



const orderInfo = async (req, res) => {
  try {
    const order = await Order.findOne({ phoneNumber: req.user.phoneNumber });

    return res.json({ success: order != null, order });
  } catch (err) {
    logger.error("Error in order check:", err);
    return res.json({ success: false, err });
  }
};

const driverInfo = async (req, res) => {
  try {
    const driverProfile = await DriverProfile.findOne({
      phoneNumber: req.user.phoneNumber,
    });
    return res.json({ success: driverProfile != null, driverProfile });
  } catch (err) {
    logger.error("Error getting driver info:", err);
    return res.json({ success: false, err });
  }
};

const orderConfirm = async (req, res) => {
  try {
    const { phoneNumber, CarModel, AvialableTime, address, isSpareKey } =
      req.body;
    let order = await Order.findOne({ phoneNumber });
    if (order == null) {
      order = new Order({
        //test
        phoneNumber,
        CarModel,
        AvialableTime,
        address,
        isSpareKey,
      });
      await order.save();
      const qpay = await getBankList(req, res, req.user.$set_id);

      //  if (response != null) {

      res.json({ success: true, message: "Order sucess", qpay });
    } else {
      if (!order.paid) {
        const qpay = await getBankList(req, res, req.user._id);
        return res.json({
          success: false,
          message: `Already submitted order `,
          order,
          qpay,
        });
      }
      return res.json({
        success: false,
        message: `Already submitted order and paid, invoice:${order.realInvoiceId}`,
        order,
      });
    }
  } catch (err) {
    logger.error("Error in order confirmation:", err);
    return res.json({ success: false, message: "Fatal Error" });
  }
};



const register = async (req, res) => {
  try {
    const {
      deviceNumber,
      type,
      uix,
      isDefault,
      _id,
      phoneNumber,
      version,
      rentable,
      deviceName,
    } = req.body;
    let _version = version ? parseFloat(version) : 1;

    // Check if deviceNumber is empty or undefined
    if (!deviceNumber) {
      return res.json({
        success: false,
        message: "Device number is required",
      });
    }

    if (deviceNumber) {
      const dev = await Device.findOne({ deviceNumber });
      // Only check for conflicts if it's a different device (not the one being updated)
      if (dev != null && phoneNumber != dev.phoneNumber && dev._id.toString() !== _id) {
        return res.json({
          success: false,
          message: "Бүртгэлтэй төхөөрөмж дахин бүртгэгдэх боломжгүй",
        });
      }
    }

    let device = null;

    if (_id != "") device = await Device.findById(_id);
    if (isDefault) {
      await Device.updateMany(
        { phoneNumber: phoneNumber ? phoneNumber : req.user.phoneNumber },
        { isDefault: false }
      );
    }
    if (device != null) {
      await Device.findByIdAndUpdate(_id, {
        deviceNumber,
        uix,
        type,
        isDefault: isDefault == true ? isDefault : device.isDefault,
        version: _version,

        deviceName,
      });
      res.json({ success: true });
    } else {
      device = new Device({
        phoneNumber: phoneNumber ? phoneNumber : req.user.phoneNumber,
        deviceNumber,
        type,
        uix,
        version: _version,
        isDefault,

        deviceName,
      });
      await device.save();
      res.json({ success: true });
    }
    if (
      (uix.toLowerCase() == "carv1.2" || uix.toLowerCase() === "car2.2") &&
      isDefault
    ) {
      subscribeCar2(deviceNumber);
    }
  } catch (err) {
    console.log(err);
    return res.json({ success: false });
  }
};


const deleteDevice = async (req, res) => {
  try {
    // Authorization is handled by the admin middleware in routes
    // Find the device by deviceNumber and delete it
    const device = await Device.findOneAndDelete({ deviceNumber: req.body.deviceNumber });

    // If the device is not found, return a 404 error
    if (!device) {
      return res.status(404).json({ success: false, message: 'Device not found' });
    }

    return res.json({ success: true });
  } catch (err) {
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

const deleteMultipleDevices = async (req, res) => {
  try {
    // Authorization is handled by the admin middleware in routes
    const { ids } = req.body;
    await Device.deleteMany({ _id: { $in: ids } });
    return res.json({ success: true });
  } catch (err) {
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

const updateDevice = async (req, res) => {
  // console.log(req.body);
  try {
    const { deviceNumber, type, uix, renter } = req.body;

    const deviceUpdate = await Device.findByIdAndUpdate(req.params.id, {
      $set: {
        deviceNumber,
        type,
        uix,
        renter,
      },
    });

    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({ success: false });
  }
};

const controlAnalog = async (req, res) => {
  const device = await Device.findOne({ phoneNumber: req.user.phoneNumber });
  // req.body.analog = {
  //     TH: 123,
  //     TL: 3
  // };
  if (device != null && device) {
    const id = device ? device.deviceNumber : 12345;

    const data = {
      topic: "gps/command",
      payload: {
        id: id,
        command: req.body.analog,
      },
      qos: 0,
      retain: false,
      clientid: id,
    };

    if (device.type == "4g") {
      const options = {
        auth: {
          username: process.env.MQTT_USER_NAME,
          password: process.env.MQTT_USER_PWD,
        },
      };
      const response = await sendMqtt(data, options, res);
      if (response != null && response.data) {
        const log = new LogModel();
        log.user = req.user._id;
        log.userId = req.user._id;
        log.deviceNumber = id;
        log.command = "set analog with 4g";
        log.commandType = "config";
        log.sent = "yes";
        log.success = true;
        log.response = response.data;
        log.responseStatus = "success";
        log.sentTime = new Date();
        log.receiveTime = new Date();
        log.responseTime = new Date() - log.sentTime;
        log.responseType = "HTTP";
        log.deviceOnline = true;
        await log.save();

        // Publish real-time statistics update
        // statisticsMqttPublisher.publishCommandUpdate({
        //   userId: req.user._id,
        //   deviceNumber: id,
        //   command: "set analog with 4g",
        //   commandType: "config",
        //   success: true,
        //   responseTime: log.responseTime
        // }); // Disabled - using direct MQTT capture instead
        res.json({
          success: true,
          action: req.params.cmd,
          result: response.data,
        });
      } else {
        const log = new LogModel();
        log.deviceNumber = id;
        log.user = req.user._id;
        log.userId = req.user._id;
        log.command = "set analog with 4g";
        log.commandType = "config";
        log.sent = "false";
        log.success = false;
        log.response = "can not send the analog values for setting";
        log.responseStatus = "failed";
        log.failureReason = "Connection failed";
        log.sentTime = new Date();
        log.responseType = "HTTP";
        log.deviceOnline = false;
        await log.save();

        // Publish real-time statistics update
        // statisticsMqttPublisher.publishCommandUpdate({
        //   userId: req.user._id,
        //   deviceNumber: id,
        //   command: "set analog with 4g",
        //   commandType: "config",
        //   success: false,
        //   failureReason: "Connection failed"
        // }); // Disabled - using direct MQTT capture instead
        res.json({
          success: false,
          action: req.params.cmd,
          err: "can not set values ...",
        });
      }
    }

  } else {
    const log = new LogModel();
    log.deviceNumber = id;
    log.user = req.user._id;
    log.command = `${req.params.cmd} with sms`;
    log.sent = "no";
    log.deviceType = "none";
    log.message = "Not found device";
    log.sentTime = new Date();
    log.responseType = "HTTP";
    log.save();
    return res.json({
      success: false,
      action: req.params.cmd,
      err: "not found device...",
    });
  }
};

const controlDevice = async (req, res) => {
  try {
    const { deviceNumber, time1, time2, minTemp, maxTemp } = req.body;

    const devices = await Device.find({ deviceNumber });

    if (devices != null && devices.length > 0) {
      // let device = devices.filter(d => d.isDefault)[0];
      let device = null;
      if (deviceNumber && deviceNumber != "") {
        device = devices.filter((d) => `${d.deviceNumber}` == deviceNumber)[0];
      }
      const id = device ? device.deviceNumber : 12345;
      const uix = device ? device.uix : "CarV1.0";
      let cmd = cmds[req.params.cmd];
      if (req.params.cmd == ":turnoff" && uix.includes("Car2")) {
        cmd = "unt";
      } else if (req.params.cmd == ":turnon" && uix.includes("Car2")) {
        cmd = "asa";
      }
      const data = {
        topic: id,
        payload: {
          id: id,
          command: cmd, //here the command passed via
        },
        qos: 0,
        retain: false,
        clientid: id,
      };
      if (time1) {
        data.timer = parseFloat(time1);
      }
      if (device.type == "4g") {
        // console.log(device.uix);

        if (device.uix.toLowerCase() == "carv1.2") {
          data.topic = `${id}`;
          data.payload = `${cmd}`;
          if (req.params.cmd == ":temp") {
            data.temp = [minTemp, maxTemp];
          } else {
            data.timer = parseFloat(time1);
          }

          const result = await publish(data, false);
          res.json({
            success: result == "success",
            action: req.params.cmd,
            result,
          });
        } else {
          const options = {
            auth: {
              username: process.env.MQTT_USER_NAME,
              password: process.env.MQTT_USER_PWD,
            },
          };
          if (req.params.cmd == ":temp") {
            // data.temp = [minTemp, maxTemp];
            data.payload.temp = `${minTemp}x${maxTemp}`;
          }
          const response = await sendMqtt(data, options, res);
          if (response != null && response.data) {
              res.json({
              success: true,
              action: req.params.cmd,
              result: response.data,
            });
          } else {
             res.json({ success: false, action: req.params.cmd, err: " ..." });
          }
        }
      }

    } else {
      res.json({
        success: false,
        action: req.params.cmd,
        err: "can not send request ...",
      });
      // return res.json({ success: false, action: req.params.cmd, err: "not found device..." });
    }
  } catch (err) {
    console.log(err);
    res.json({ success: false, err });
  }
};

const changeActive = async (req, res) => {
  try {
    await Device.updateMany(
      { _id: { $in: req.body.ids } },
      {
        $set: {
          status: req.body.status,
        },
      }
    );
    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({
      success: false,
      err,
    });
  }
};

const checkLine = async (req, res) => {
  try {
    const { deviceNumber } = req.body;

    const options = {
      auth: {
        username: process.env.MQTT_USER_NAME,
        password: process.env.MQTT_USER_PWD,
      },
    };
    const device = await Device.findOne({ deviceNumber });
    // console.log(device)
    if (device) {
      // car2 == car2.2,
      if (
        device.uix.toLowerCase() == "carv1.2" ||
        device.uix.toLowerCase() == "car2.2"
      )
        subscribeCar2(deviceNumber);
      else {
        unsubscribeCar2(deviceNumber);
      }
    }
    const checked = await checkMqttClient(deviceNumber, options);
    if (checked) {
      // await Order.findOneAndUpdate({phoneNumber:device.phoneNumber},{isInstalled:true});
      await Order.findOneAndUpdate(
        { phoneNumber: device.phoneNumber },
        { isInstalled: true }
      );

      res.json({ status: "online" });
      // console.log('online')
    } else {
      res.json({ status: "offline" });
      // console.log('offline')
    }
  } catch (err) {
    //console.log(err)
    res.json({ status: "error" });
    //console.log('err')
  }
};

const setGpsTime = async (req, res) => {
  try {
    const { deviceNumber, gpsTime } = req.body;
    if (gpsTime > 0) {
      let hex = `000000${parseInt(gpsTime / 5 / 60).toString(16)}`.slice(-6);

      const data = {
        topic: `${deviceNumber}/event`,
        payload: `050000${hex}00`,
        qos: 2,
        retain: true,
      };

      const options = {
        auth: {
          username: process.env.MQTT_USER_NAME,
          password: process.env.MQTT_USER_PWD,
        },
      };
      const response = await sendMqtt(data, options, res);
      // console.log(response);
    }
    await Device.findOneAndUpdate({ deviceNumber }, { interval: gpsTime });
    res.json({ message: "updated gps interval time", success: true });
  } catch (err) {
    res.json({ message: "failed to update gps interval time", success: false });
  }
};

const switchSubscribe = async (req, res) => {
  try {
    const { deviceNumber } = req.body;
    const device = await Device.findOne({ deviceNumber });
    const subscribed = device.subscribed;
    let action = false;
    if (device) {
    }
    if (subscribed) {
      action = unsubscribe(deviceNumber);
    }
    if (!subscribed) {
      action = subscribe(deviceNumber);
    }

    if (action) {
      device.subscribed = !subscribed;
      await device.save();
      res.json({
        message: `Update subscribe option to ${
          subscribed ? "unsubscribe" : "subsribe"
        }`,
        subscribed,
        success: true,
      });
    } else {
      res.json({
        message: `Failed to ${subscribed ? "unsubscribe" : "subsribe"}`,
        subscribed,
        success: false,
      });
    }
  } catch (err) {
    res.json({ message: "failed to update subscribed option", success: false });
  }
};

const addBleDevice = async (req, res) => {
  try {
    const { deviceNumber, ble, bleIndex } = req.body;

    const device = await Device.findOne({ deviceNumber });

    const bles = device.bles;
    const current = bles.filter((b) => b.id == ble);
    const hex = `0${bleIndex}01${ble.replaceAll(/:/gi, "")}`;

    if (current.length == 0) {
      const data = {
        topic: `${deviceNumber}/event`,
        payload: hex,
        qos: 2,
        retain: true,
      };
      const options = {
        auth: {
          username: process.env.MQTT_USER_NAME,
          password: process.env.MQTT_USER_PWD,
        },
      };
      const result = await publish(data);
      // console.log(result);
      // const response = await sendMqtt(data, options, res);
      if (result == "success") {
        bles.push({ id: ble, index: bleIndex });
        device.bles = bles;

        await device.save();

        res.json({ message: `Updated ble devices successful`, success: true });
      } else {
        res.json({ message: `Failed to add ble device`, success: false });
      }
    } else {
      res.json({
        message: `Already Exist with ble number ${ble}`,
        success: false,
      });
    }
  } catch (err) {
    console.log(err);
    res.json({ message: "Failed to register ble device", success: false });
  }
};

const removeBleDevice = async (req, res) => {
  try {
    const { deviceNumber, ble, bleIndex } = req.body;
    const device = await Device.findOne({ deviceNumber });
    const bles = device.bles.filter((b) => b.id == ble);

    if (bles.length == 1) {
      const hex = `0${bles[0].index}00${ble.replaceAll(/:/gi, "")}`;
      const data = {
        topic: `${deviceNumber}/event`,
        payload: hex,
        qos: 2,
        retain: true,
      };
      const result = await publish(data);
    if (result == "success") {
        device.bles = device.bles.filter((b) => b.id != ble);
        await device.save();
        res.json({ message: `Remove ble device successful`, success: true });
      } else {
        res.json({
          message: `Not remove ble device, mqtt error`,
          success: false,
        });
      }
    } else {
      res.json({ message: `Not Exist with ble number ${ble}`, success: false });
    }
  } catch (err) {
    console.log(err);
    res.json({ message: "Failed to remove ble device", success: false });
  }
};

const getLocations = async (req, res) => {
  const devices = await Device.find({});
  if (devices != null && devices.length > 0) {
    devices
      .filter((d) => d.isDefault)
      .map(async (device) => {
        const id = device ? device.deviceNumber : 12345;
        const uix = device ? device.uix : "CarV1.0";
        const data = {
          topic: id,
          payload: {
            id: id,
            command: "check",
          },
          qos: 0,
          retain: false,
          clientid: id,
        };

        if (device.type == "4g") {
          // console.log(device.uix);

          if (device.uix.toLowerCase() == "carv1.2") {
            data.topic = `${id}`;
            data.payload = `${cmd}`;
            const result = await publish(data, false);
            // console.log({ result });
            if (result) subscribeCar2(devices);
            res.json({
              success: result == "success",
              action: req.params.cmd,
              result,
            });
          }
        }
      });
  } else {
    return res.json({
      success: false,
      action: req.params.cmd,
      err: "not found registered devices...",
    });
  }
};




const extendsBalance = getBankList;










const setDriverProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);
    if (req.file && user != null) {
      user.driverLicenseFile = req.file.path;
    }
    if (user != null) {
      user.driverLicenseVerification = user?.driverLicenseVerification || 1;
      user.username = req.body?.username || "";
      user.address = req.body?.address || "";
      user.description = req.body?.description || "";
      await user.save();
      return res.json({ success: true });
    } else {
      return res.status(201).json({ sucess: false, err: "can not find user" });
    }
  } catch (err) {
    console.log(err);
    res.json({ success: false, err });
  }
};

const sendSimCheckCommand = async (req, res) => {
  const { deviceNumber } = req.body;

  // Validate deviceNumber
  if (!deviceNumber || typeof deviceNumber !== 'string') {
    return res.status(400).json({ success: false, message: "Invalid device number" });
  }

  try {
    const device = await Device.findOne({ deviceNumber });

    if (!device) {
      return res.status(404).json({ success: false, message: "Device not found" });
    }

    if (device.uix.toLowerCase() === "carv1.2" || device.uix.toLowerCase() === "car2.2") {
      const data = { topic: `${deviceNumber}`, payload: `sim` };
      const result = await publish(data, false);

      return res.status(result === "success" ? 200 : 500)
                .json({ success: result === "success" });
    }

    return res.status(400).json({ success: false, message: "Device does not meet the criteria" });

  } catch (error) {
    console.error("Error in sendSimCheckCommand: ", error);
    return res.status(500).json({ success: false, message: "Internal server error" });
  }
};

const updateDeviceRenter = async (req, res) => {
  try {
    const { deviceId, renter } = req.body;
    
    if (!deviceId) {
      return res.status(400).json({ 
        success: false, 
        message: "Device ID is required" 
      });
    }
    
    // Check if the device exists
    const device = await Device.findById(deviceId);
    if (!device) {
      return res.status(404).json({ 
        success: false, 
        message: "Device not found" 
      });
    }
    
    // Update the renter field
    device.renter = renter || "";
    await device.save();
    
    return res.json({ 
      success: true, 
      message: "Device renter updated successfully",
      device: {
        _id: device._id,
        deviceNumber: device.deviceNumber,
        renter: device.renter
      }
    });
  } catch (err) {
    console.error("Error updating device renter:", err);
    return res.status(500).json({ 
      success: false, 
      message: "Server error",
      error: err.message
    });
  }
};

const getDeviceVersions = async (req, res) => {
  try {
    // Get latest device message logs with version information
    const deviceVersions = await DeviceMessageLogModel.aggregate([
      {
        $match: {
          payloads: { $exists: true, $ne: [] }
        }
      },
      {
        $sort: { _id: -1 } // Sort by latest first
      },
      {
        $group: {
          _id: "$deviceNumber",
          latestLog: { $first: "$$ROOT" }
        }
      },
      {
        $project: {
          deviceNumber: "$_id",
          version: {
            $let: {
              vars: {
                latestPayload: { $arrayElemAt: ["$latestLog.payloads", -1] }
              },
              in: {
                $cond: {
                  if: { $type: "$$latestPayload" },
                  then: {
                    $cond: [
                      { $ne: [{ $type: "$$latestPayload.ver" }, "missing"] },
                      "$$latestPayload.ver",
                      {
                        $cond: [
                          { $ne: [{ $type: "$$latestPayload.version" }, "missing"] },
                          "$$latestPayload.version",
                          {
                            $cond: [
                              { $ne: [{ $type: "$$latestPayload.firmware" }, "missing"] },
                              "$$latestPayload.firmware",
                              {
                                $cond: [
                                  { $ne: [{ $type: "$$latestPayload.fw" }, "missing"] },
                                  "$$latestPayload.fw",
                                  null
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  },
                  else: null
                }
              }
            }
          },
          lastUpdate: "$latestLog.received"
        }
      },
      {
        $match: {
          version: { $ne: null }
        }
      }
    ]);

    // Convert to a map for easy lookup
    const versionMap = {};
    deviceVersions.forEach(item => {
      versionMap[item.deviceNumber] = {
        version: item.version,
        lastUpdate: item.lastUpdate
      };
    });

    res.json({
      success: true,
      versions: versionMap
    });

  } catch (err) {
    console.error("Error getting device versions:", err);
    res.json({
      success: false,
      err: err.message,
      versions: {}
    });
  }
};

module.exports = {
  driverConfirm,
  getConnectedMqttClients,
  getDevices,
  getAll,
  editDevice,
  myDevice,
  setByAdmin,

  orderInfo,
  driverInfo,
  orderConfirm,

  register,
  deleteDevice,
  updateDevice,
  controlAnalog,
  controlDevice,
  changeActive,
  checkLine,
  setGpsTime,
  switchSubscribe,
  addBleDevice,
  removeBleDevice,
  getLocations,
  extendsBalance,
  setDriverProfile,
  listAll,
  sendSimCheckCommand,
  deleteMultipleDevices,
  updateDeviceRenter,
  getDeviceVersions
};
